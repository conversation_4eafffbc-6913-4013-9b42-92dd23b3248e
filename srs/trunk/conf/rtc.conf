# WebRTC推流服务器配置文件
# 专门用于接收前端WebRTC推流并转换为RTMP，便于后续录制

listen              1935;
max_connections     1000;
daemon              off;
srs_log_tank        console;

# HTTP服务器配置 - 提供Web界面和API
http_server {
    enabled         on;
    listen          8080;
    dir             ./objs/nginx/html;
    # HTTPS支持
    https {
        enabled     on;
        listen      8443;
        key         ./conf/server.key;
        cert        ./conf/server.crt;
    }
}

# HTTP API配置
http_api {
    enabled         on;
    listen          1985;
}

# 统计信息配置
stats {
    network         0;
}

# WebRTC服务器配置
rtc_server {
    enabled         on;
    listen          8000;  # UDP端口
    # 候选地址配置 - 可通过环境变量CANDIDATE设置
    candidate       $CANDIDATE;

    # TCP传输支持（用于防火墙穿透）
    tcp {
        enabled     on;
        listen      8000;
    }

    # 支持UDP和TCP协议
    protocol        all;
}

# 默认虚拟主机配置
vhost __defaultVhost__ {
    # WebRTC配置
    rtc {
        enabled         on;
        # 禁用RTMP到WebRTC转换（我们只需要WebRTC推流）
        rtmp_to_rtc     off;
        # 启用WebRTC到RTMP转换（用于录制）
        rtc_to_rtmp     on;

        # 启用NACK重传机制
        nack            on;
        # 启用TWCC拥塞控制
        twcc            on;
        # STUN超时设置
        stun_timeout    30;
    }

    # HTTP-FLV直播流配置
    http_remux {
        enabled         on;
        mount           [vhost]/[app]/[stream].flv;
    }

    # HLS配置（可选）
    hls {
        enabled         on;
        hls_fragment    10;
        hls_window      60;
        hls_path        ./objs/nginx/html;
        hls_m3u8_file   [app]/[stream].m3u8;
        hls_ts_file     [app]/[stream]-[seq].ts;
    }
}

