#!/bin/bash

# SRS WebRTC推流服务器启动脚本

echo "=========================================="
echo "SRS WebRTC推流服务器启动脚本"
echo "=========================================="

# 检查SRS是否已编译
if [ ! -f "srs/trunk/objs/srs" ]; then
    echo "错误: SRS未编译，请先编译SRS"
    echo "编译命令:"
    echo "  cd srs/trunk"
    echo "  ./configure --rtc=on --https=on"
    echo "  make -j4"
    exit 1
fi

# 检查配置文件是否存在
if [ ! -f "srs/trunk/conf/rtc.conf" ]; then
    echo "错误: 配置文件不存在: srs/trunk/conf/rtc.conf"
    exit 1
fi

# 检查SSL证书是否存在
if [ ! -f "srs/trunk/conf/server.key" ] || [ ! -f "srs/trunk/conf/server.crt" ]; then
    echo "警告: SSL证书不存在，正在生成自签名证书..."
    cd srs/trunk
    openssl req -newkey rsa:2048 -new -nodes -x509 -days 3650 \
        -keyout conf/server.key -out conf/server.crt \
        -subj "/C=CN/ST=Beijing/L=Beijing/O=SRS/OU=WebRTC/CN=localhost"
    cd ../..
    echo "SSL证书生成完成"
fi

# 检查端口是否被占用
check_port() {
    local port=$1
    local service=$2
    if netstat -tuln 2>/dev/null | grep -q ":$port " || ss -tuln 2>/dev/null | grep -q ":$port "; then
        echo "警告: 端口 $port ($service) 已被占用"
        echo "请检查是否有其他SRS实例正在运行"
        return 1
    fi
    return 0
}

echo "检查端口占用情况..."
check_port 1935 "RTMP"
check_port 8000 "WebRTC"
check_port 1985 "HTTP API"
check_port 8080 "HTTP Server"
check_port 8443 "HTTPS Server"

# 创建必要的目录
echo "创建必要的目录..."
mkdir -p srs/trunk/objs/nginx/html/live

# 复制前端文件到SRS的web目录
echo "复制前端文件..."
if [ -f "web/webrtc_publisher.html" ]; then
    cp web/webrtc_publisher.html srs/trunk/objs/nginx/html/
    echo "前端推流页面已复制到: srs/trunk/objs/nginx/html/webrtc_publisher.html"
else
    echo "警告: 前端文件不存在: web/webrtc_publisher.html"
fi

# 获取本机IP地址
get_local_ip() {
    # 尝试多种方法获取本机IP
    local ip=""
    
    # 方法1: 使用hostname命令
    ip=$(hostname -I 2>/dev/null | awk '{print $1}')
    if [ -n "$ip" ] && [ "$ip" != "127.0.0.1" ]; then
        echo "$ip"
        return
    fi
    
    # 方法2: 使用ip命令
    ip=$(ip route get ******* 2>/dev/null | grep -oP 'src \K\S+')
    if [ -n "$ip" ] && [ "$ip" != "127.0.0.1" ]; then
        echo "$ip"
        return
    fi
    
    # 方法3: 使用ifconfig命令
    ip=$(ifconfig 2>/dev/null | grep -E 'inet.*broadcast' | grep -v '127.0.0.1' | awk '{print $2}' | head -1)
    if [ -n "$ip" ]; then
        echo "$ip"
        return
    fi
    
    # 默认返回localhost
    echo "localhost"
}

# 设置环境变量
LOCAL_IP=$(get_local_ip)
export CANDIDATE=$LOCAL_IP

# 进入SRS目录
cd srs/trunk

echo ""
echo "启动SRS服务器..."
echo "配置文件: conf/rtc.conf"
echo "服务器IP: $CANDIDATE"
echo ""
echo "服务端口:"
echo "  - RTMP推流: 1935"
echo "  - WebRTC: 8000 (UDP+TCP)"
echo "  - HTTP API: 1985"
echo "  - HTTP服务器: 8080"
echo "  - HTTPS服务器: 8443"
echo ""
echo "🔒 推荐使用HTTPS推流页面: https://$LOCAL_IP:8443/webrtc_publisher.html"
echo "📄 HTTP推流页面: http://$LOCAL_IP:8080/webrtc_publisher.html"
echo "🎛️ SRS管理页面: http://$LOCAL_IP:8080/"
echo ""
echo "按 Ctrl+C 停止服务器"
echo "=========================================="

# 启动SRS服务器
./objs/srs -c conf/rtc.conf
