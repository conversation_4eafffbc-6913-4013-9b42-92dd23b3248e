<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebRTC推流测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .video-container {
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }
        .video-box {
            flex: 1;
            text-align: center;
        }
        video {
            width: 100%;
            max-width: 400px;
            height: 300px;
            background: #000;
            border-radius: 8px;
        }
        .controls {
            margin: 20px 0;
            text-align: center;
        }
        .control-group {
            margin: 10px 0;
        }
        label {
            display: inline-block;
            width: 120px;
            text-align: right;
            margin-right: 10px;
        }
        input, select, button {
            padding: 8px 12px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background: #007bff;
            color: white;
            cursor: pointer;
            font-size: 16px;
            min-width: 120px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            text-align: center;
            font-weight: bold;
        }
        .status-success { background: #d4edda; color: #155724; }
        .status-error { background: #f8d7da; color: #721c24; }
        .status-warning { background: #fff3cd; color: #856404; }
        .info-panel {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
        .play-urls {
            background: #e9ecef;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
        .url-item {
            margin: 5px 0;
            font-family: monospace;
            background: white;
            padding: 5px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎥 WebRTC推流测试页面</h1>
        
        <div class="video-container">
            <div class="video-box">
                <h3>本地摄像头</h3>
                <video id="localVideo" autoplay muted playsinline></video>
            </div>
        </div>

        <div class="controls">
            <div class="control-group">
                <label>服务器地址:</label>
                <input type="text" id="serverUrl" value="localhost" placeholder="服务器IP或域名">
            </div>
            
            <div class="control-group">
                <label>流名称:</label>
                <input type="text" id="streamName" value="livestream" placeholder="流名称">
            </div>
            
            <div class="control-group">
                <label>视频质量:</label>
                <select id="videoQuality">
                    <option value="640x480">640x480 (标清)</option>
                    <option value="1280x720" selected>1280x720 (高清)</option>
                    <option value="1920x1080">1920x1080 (全高清)</option>
                </select>
            </div>

            <div class="control-group">
                <button id="startBtn">开始推流</button>
                <button id="stopBtn" disabled>停止推流</button>
            </div>
        </div>

        <div id="status" class="status" style="display: none;"></div>

        <div class="info-panel">
            <h3>📊 推流信息</h3>
            <p><strong>推流地址:</strong> <span id="publishUrl">-</span></p>
            <p><strong>状态:</strong> <span id="connectionStatus">未连接</span></p>
            <p><strong>推流时长:</strong> <span id="duration">00:00:00</span></p>
            <p><strong>会话ID:</strong> <span id="sessionId">-</span></p>
        </div>

        <div class="play-urls">
            <h3>📺 播放地址</h3>
            <p><strong>WebRTC播放:</strong></p>
            <div class="url-item" id="webrtcPlayUrl">webrtc://localhost:8000/live/livestream</div>
            
            <p><strong>HTTP-FLV播放:</strong></p>
            <div class="url-item" id="flvPlayUrl">http://localhost:8080/live/livestream.flv</div>
            
            <p><strong>HLS播放:</strong></p>
            <div class="url-item" id="hlsPlayUrl">http://localhost:8080/live/livestream.m3u8</div>
        </div>

        <div class="info-panel">
            <h3>📝 使用说明</h3>
            <ol>
                <li>确保SRS服务器已启动</li>
                <li>在服务器地址中输入SRS服务器的IP地址</li>
                <li>设置流名称（可自定义）</li>
                <li>选择合适的视频质量</li>
                <li>点击"开始推流"按钮</li>
                <li>授权浏览器访问摄像头和麦克风</li>
                <li>推流成功后可使用上方的播放地址进行播放测试</li>
            </ol>
            <p><strong>注意:</strong> WebRTC需要HTTPS环境，建议使用 <code>https://服务器IP:8443/webrtc_publisher.html</code> 访问</p>
        </div>
    </div>

    <!-- SRS WebRTC SDK - 使用本地版本 -->
    <script>
        // 简化的WebRTC推流实现
        class SrsRtcPublisherAsync {
            constructor() {
                this.pc = null;
                this.stream = null;
                this.constraints = {
                    audio: true,
                    video: { width: 1280, height: 720 }
                };
            }

            async publish(url) {
                try {
                    // 获取用户媒体
                    this.stream = await navigator.mediaDevices.getUserMedia(this.constraints);

                    // 创建RTCPeerConnection
                    this.pc = new RTCPeerConnection({
                        iceServers: [{ urls: 'stun:stun.l.google.com:19302' }]
                    });

                    // 添加本地流到PeerConnection
                    this.stream.getTracks().forEach(track => {
                        this.pc.addTrack(track, this.stream);
                    });

                    // 创建offer
                    const offer = await this.pc.createOffer();
                    await this.pc.setLocalDescription(offer);

                    // 解析URL
                    const urlObj = new URL(url.replace('webrtc://', 'http://'));
                    const apiUrl = `http://${urlObj.host.replace(':8000', ':1985')}/rtc/v1/publish/`;

                    // 发送offer到SRS
                    const response = await fetch(apiUrl, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            api: apiUrl,
                            streamurl: url,
                            sdp: offer.sdp
                        })
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    const data = await response.json();

                    if (data.code !== 0) {
                        throw new Error(`SRS Error ${data.code}: ${data.msg || 'Unknown error'}`);
                    }

                    // 设置远程描述
                    await this.pc.setRemoteDescription({
                        type: 'answer',
                        sdp: data.sdp
                    });

                    return {
                        sessionid: data.sessionid || 'connected',
                        server: data.server || 'SRS'
                    };

                } catch (error) {
                    this.close();
                    throw error;
                }
            }

            close() {
                if (this.stream) {
                    this.stream.getTracks().forEach(track => track.stop());
                    this.stream = null;
                }
                if (this.pc) {
                    this.pc.close();
                    this.pc = null;
                }
            }
        }

        // 确保SDK可用
        window.SrsRtcPublisherAsync = SrsRtcPublisherAsync;
    </script>
    
    <script>
        // 页面元素
        const localVideo = document.getElementById('localVideo');
        const serverUrl = document.getElementById('serverUrl');
        const streamName = document.getElementById('streamName');
        const videoQuality = document.getElementById('videoQuality');
        const startBtn = document.getElementById('startBtn');
        const stopBtn = document.getElementById('stopBtn');
        const status = document.getElementById('status');
        const publishUrl = document.getElementById('publishUrl');
        const connectionStatus = document.getElementById('connectionStatus');
        const duration = document.getElementById('duration');
        const sessionId = document.getElementById('sessionId');
        const webrtcPlayUrl = document.getElementById('webrtcPlayUrl');
        const flvPlayUrl = document.getElementById('flvPlayUrl');
        const hlsPlayUrl = document.getElementById('hlsPlayUrl');

        // 全局变量
        let sdk = null;
        let isPublishing = false;
        let startTime = null;
        let durationTimer = null;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 自动检测服务器地址
            const hostname = window.location.hostname;
            if (hostname && hostname !== 'localhost' && hostname !== '127.0.0.1') {
                serverUrl.value = hostname;
            }

            updateUrls();

            // 事件监听
            startBtn.addEventListener('click', startPublish);
            stopBtn.addEventListener('click', stopPublish);
            serverUrl.addEventListener('input', updateUrls);
            streamName.addEventListener('input', updateUrls);
        });

        // 更新URL显示
        function updateUrls() {
            const server = serverUrl.value || 'localhost';
            const stream = streamName.value || 'livestream';
            
            publishUrl.textContent = `webrtc://${server}:8000/live/${stream}`;
            webrtcPlayUrl.textContent = `webrtc://${server}:8000/live/${stream}`;
            flvPlayUrl.textContent = `http://${server}:8080/live/${stream}.flv`;
            hlsPlayUrl.textContent = `http://${server}:8080/live/${stream}.m3u8`;
        }

        // 显示状态信息
        function showStatus(message, type = 'info') {
            status.textContent = message;
            status.className = `status status-${type}`;
            status.style.display = 'block';
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        // 更新推流时长
        function updateDuration() {
            if (startTime) {
                const elapsed = Math.floor((Date.now() - startTime) / 1000);
                const hours = Math.floor(elapsed / 3600).toString().padStart(2, '0');
                const minutes = Math.floor((elapsed % 3600) / 60).toString().padStart(2, '0');
                const seconds = (elapsed % 60).toString().padStart(2, '0');
                duration.textContent = `${hours}:${minutes}:${seconds}`;
            }
        }

        // 获取视频约束
        function getVideoConstraints() {
            const quality = videoQuality.value;
            const [width, height] = quality.split('x').map(Number);

            return {
                audio: {
                    echoCancellation: true,
                    noiseSuppression: true,
                    autoGainControl: true
                },
                video: {
                    width: { ideal: width, max: width },
                    height: { ideal: height, max: height },
                    frameRate: { ideal: 30, max: 30 }
                }
            };
        }

        // 开始推流
        async function startPublish() {
            try {
                showStatus('🔄 正在连接...', 'warning');
                startBtn.disabled = true;

                // 创建SDK实例
                if (sdk) {
                    sdk.close();
                }
                sdk = new SrsRtcPublisherAsync();

                // 设置约束
                sdk.constraints = getVideoConstraints();

                // 设置本地视频流
                localVideo.srcObject = sdk.stream;

                // 开始推流
                const url = publishUrl.textContent;
                console.log('🚀 开始推流到:', url);
                const session = await sdk.publish(url);

                // 推流成功
                isPublishing = true;
                startTime = Date.now();
                showStatus('🎥 推流中', 'success');
                connectionStatus.textContent = '已连接';
                sessionId.textContent = session.sessionid || '已连接';

                // 启动时长计时器
                durationTimer = setInterval(updateDuration, 1000);

                // 更新按钮状态
                startBtn.disabled = true;
                stopBtn.disabled = false;

                console.log('✅ 推流成功:', session);

            } catch (error) {
                console.error('❌ 推流失败:', error);
                showStatus(`❌ 推流失败: ${error.message}`, 'error');
                startBtn.disabled = false;
                connectionStatus.textContent = '连接失败';
            }
        }

        // 停止推流
        function stopPublish() {
            try {
                if (sdk) {
                    sdk.close();
                    sdk = null;
                }

                if (durationTimer) {
                    clearInterval(durationTimer);
                    durationTimer = null;
                }

                isPublishing = false;
                startTime = null;

                showStatus('⏹️ 推流已停止', 'warning');
                connectionStatus.textContent = '未连接';
                sessionId.textContent = '-';
                duration.textContent = '00:00:00';

                // 更新按钮状态
                startBtn.disabled = false;
                stopBtn.disabled = true;

                // 清除本地视频
                if (localVideo.srcObject) {
                    const tracks = localVideo.srcObject.getTracks();
                    tracks.forEach(track => track.stop());
                    localVideo.srcObject = null;
                }

                console.log('⏹️ 推流已停止');

            } catch (error) {
                console.error('停止推流时出错:', error);
                showStatus(`停止推流时出错: ${error.message}`, 'error');
            }
        }

        // 页面卸载时清理资源
        window.addEventListener('beforeunload', function() {
            if (isPublishing) {
                stopPublish();
            }
        });
    </script>
</body>
</html>
